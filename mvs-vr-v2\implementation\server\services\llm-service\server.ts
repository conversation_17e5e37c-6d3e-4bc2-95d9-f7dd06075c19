/**
 * LLM Integration Service Server
 * Handles LLM requests, conversation management, and provider routing
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { logger } from '../../shared/utils/logger';
import { LLMService } from './index';

const app = express();
const PORT = process.env.SERVICE_PORT || 3004;

// Initialize LLM service
const llmService = new LLMService();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'llm-service',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// LLM endpoints
app.post('/query', async (req, res) => {
  try {
    const { message, context, options } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    const response = await llmService.processQuery({
      message,
      context: context || {},
      options: options || {}
    });

    res.json(response);
  } catch (error) {
    logger.error('Error processing LLM query:', error);
    res.status(500).json({ error: 'Failed to process query' });
  }
});

app.post('/conversation', async (req, res) => {
  try {
    const { conversationId, message, context } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    const response = await llmService.continueConversation(conversationId, message, context);
    res.json(response);
  } catch (error) {
    logger.error('Error continuing conversation:', error);
    res.status(500).json({ error: 'Failed to continue conversation' });
  }
});

app.get('/conversation/:id', async (req, res) => {
  try {
    const conversation = await llmService.getConversation(req.params.id);
    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }
    res.json(conversation);
  } catch (error) {
    logger.error('Error getting conversation:', error);
    res.status(500).json({ error: 'Failed to get conversation' });
  }
});

app.delete('/conversation/:id', async (req, res) => {
  try {
    await llmService.deleteConversation(req.params.id);
    res.status(204).send();
  } catch (error) {
    logger.error('Error deleting conversation:', error);
    res.status(500).json({ error: 'Failed to delete conversation' });
  }
});

// Provider management endpoints
app.get('/providers', async (req, res) => {
  try {
    const providers = await llmService.getAvailableProviders();
    res.json(providers);
  } catch (error) {
    logger.error('Error getting providers:', error);
    res.status(500).json({ error: 'Failed to get providers' });
  }
});

app.get('/providers/:provider/status', async (req, res) => {
  try {
    const status = await llmService.getProviderStatus(req.params.provider);
    res.json(status);
  } catch (error) {
    logger.error('Error getting provider status:', error);
    res.status(500).json({ error: 'Failed to get provider status' });
  }
});

// Usage tracking endpoints
app.get('/usage', async (req, res) => {
  try {
    const usage = await llmService.getUsageStats(req.query);
    res.json(usage);
  } catch (error) {
    logger.error('Error getting usage stats:', error);
    res.status(500).json({ error: 'Failed to get usage stats' });
  }
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('LLM service error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`LLM Service running on port ${PORT}`);
  logger.info('Environment:', process.env.NODE_ENV);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
