services:
  # API Gateway - Entry point for all requests
  api-gateway:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.gateway
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
      - ASSET_SERVICE_URL=http://asset-service:3001
      - SCENE_SERVICE_URL=http://scene-service:3002
      - BLUEPRINT_SERVICE_URL=http://blueprint-service:3003
      - LLM_SERVICE_URL=http://llm-service:3004
      - AUTH_SERVICE_URL=http://auth-service:3005
      - ANALYTICS_SERVICE_URL=http://analytics-service:3006
      - MONITORING_SERVICE_URL=http://monitoring-service:3007
    volumes:
      - gateway-logs:/app/logs
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - redis
      - asset-service
      - scene-service
      - blueprint-service
      - llm-service
      - auth-service
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3000/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Asset Management Service
  asset-service:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.asset-service
    ports:
      - '3001:3001'
    environment:
      - NODE_ENV=production
      - SERVICE_PORT=3001
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - S3_BUCKET=${S3_BUCKET:-mvs-vr-assets}
    volumes:
      - asset-data:/app/data
      - asset-uploads:/app/uploads
      - asset-cache:/app/cache
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3001/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Scene Management Service
  scene-service:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.scene-service
    ports:
      - '3002:3002'
    environment:
      - NODE_ENV=production
      - SERVICE_PORT=3002
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
      - ASSET_SERVICE_URL=http://asset-service:3001
    volumes:
      - scene-data:/app/data
      - scene-templates:/app/templates
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
      - redis
      - asset-service
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3002/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Blueprint Management Service
  blueprint-service:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.blueprint-service
    ports:
      - '3003:3003'
    environment:
      - NODE_ENV=production
      - SERVICE_PORT=3003
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
    volumes:
      - blueprint-data:/app/data
      - blueprint-templates:/app/templates
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3003/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # LLM Integration Service
  llm-service:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.llm-service
    ports:
      - '3004:3004'
    environment:
      - NODE_ENV=production
      - SERVICE_PORT=3004
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
      - LOCAL_LLM_URL=${LOCAL_LLM_URL:-http://localhost:11434}
    volumes:
      - llm-cache:/app/cache
      - llm-models:/app/models
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3004/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Authentication Service
  auth-service:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.auth-service
    ports:
      - '3005:3005'
    environment:
      - NODE_ENV=production
      - SERVICE_PORT=3005
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret}
    volumes:
      - auth-data:/app/data
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3005/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Analytics Service
  analytics-service:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.analytics-service
    ports:
      - '3006:3006'
    environment:
      - NODE_ENV=production
      - SERVICE_PORT=3006
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
      - TENSORFLOW_BACKEND=cpu
    volumes:
      - analytics-data:/app/data
      - analytics-models:/app/models
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3006/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Monitoring Service
  monitoring-service:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.monitoring-service
    ports:
      - '3007:3007'
      - '9090:9090' # Prometheus metrics
    environment:
      - NODE_ENV=production
      - SERVICE_PORT=3007
      - PROMETHEUS_PORT=9090
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@redis:6379
    volumes:
      - monitoring-data:/app/data
      - monitoring-metrics:/app/metrics
    restart: unless-stopped
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3007/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Directus CMS
  directus:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile.directus
    ports:
      - '8055:8055'
    environment:
      - KEY=${DIRECTUS_KEY:-directus-secret-key}
      - SECRET=${DIRECTUS_SECRET:-directus-secret}
      - ADMIN_EMAIL=${DIRECTUS_ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_PASSWORD=${DIRECTUS_ADMIN_PASSWORD:-admin}
      - DB_CLIENT=pg
      - DB_HOST=supabase-db
      - DB_PORT=5432
      - DB_DATABASE=${DB_DATABASE:-postgres}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    volumes:
      - directus-uploads:/directus/uploads
    networks:
      - mvs-vr-network
    depends_on:
      - supabase-db
    restart: unless-stopped
    healthcheck:
      test:
        [
          'CMD',
          'wget',
          '--no-verbose',
          '--tries=1',
          '--spider',
          'http://localhost:8055/server/health',
        ]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  # Supabase database
  supabase-db:
    image: supabase/postgres:*********
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
    volumes:
      - supabase-db-data:/var/lib/postgresql/data
    networks:
      - mvs-vr-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'pg_isready', '-U', 'postgres']
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    networks:
      - mvs-vr-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', '${REDIS_PASSWORD:-redis_password}', 'ping']
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  # Infrastructure volumes
  supabase-db-data:
  redis-data:
  directus-uploads:

  # Gateway volumes
  gateway-logs:

  # Asset service volumes
  asset-data:
  asset-uploads:
  asset-cache:

  # Scene service volumes
  scene-data:
  scene-templates:

  # Blueprint service volumes
  blueprint-data:
  blueprint-templates:

  # LLM service volumes
  llm-cache:
  llm-models:

  # Auth service volumes
  auth-data:

  # Analytics service volumes
  analytics-data:
  analytics-models:

  # Monitoring service volumes
  monitoring-data:
  monitoring-metrics:

networks:
  mvs-vr-network:
    driver: bridge
