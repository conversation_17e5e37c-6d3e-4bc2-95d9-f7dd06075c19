/**
 * Blueprint Management Service Server
 * Handles blueprint configuration, validation, and delivery
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { logger } from '../../shared/utils/logger';
import { BlueprintService } from './blueprint-service';
import { BlueprintValidator } from './blueprint-validator';
import { BlueprintDeliveryService } from './blueprint-delivery-service';

const app = express();
const PORT = process.env.SERVICE_PORT || 3003;

// Initialize services
const blueprintService = new BlueprintService();
const blueprintValidator = new BlueprintValidator();
const blueprintDeliveryService = new BlueprintDeliveryService();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'blueprint-service',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Blueprint endpoints
app.get('/blueprints', async (req, res) => {
  try {
    const blueprints = await blueprintService.listBlueprints(req.query);
    res.json(blueprints);
  } catch (error) {
    logger.error('Error listing blueprints:', error);
    res.status(500).json({ error: 'Failed to list blueprints' });
  }
});

app.get('/blueprints/:id', async (req, res) => {
  try {
    const blueprint = await blueprintService.getBlueprint(req.params.id);
    if (!blueprint) {
      return res.status(404).json({ error: 'Blueprint not found' });
    }
    res.json(blueprint);
  } catch (error) {
    logger.error('Error getting blueprint:', error);
    res.status(500).json({ error: 'Failed to get blueprint' });
  }
});

app.post('/blueprints', async (req, res) => {
  try {
    const blueprint = await blueprintService.createBlueprint(req.body);
    res.status(201).json(blueprint);
  } catch (error) {
    logger.error('Error creating blueprint:', error);
    res.status(500).json({ error: 'Failed to create blueprint' });
  }
});

app.put('/blueprints/:id', async (req, res) => {
  try {
    const blueprint = await blueprintService.updateBlueprint(req.params.id, req.body);
    res.json(blueprint);
  } catch (error) {
    logger.error('Error updating blueprint:', error);
    res.status(500).json({ error: 'Failed to update blueprint' });
  }
});

app.delete('/blueprints/:id', async (req, res) => {
  try {
    await blueprintService.deleteBlueprint(req.params.id);
    res.status(204).send();
  } catch (error) {
    logger.error('Error deleting blueprint:', error);
    res.status(500).json({ error: 'Failed to delete blueprint' });
  }
});

// Blueprint validation endpoints
app.post('/blueprints/:id/validate', async (req, res) => {
  try {
    const result = await blueprintValidator.validateBlueprint(req.params.id);
    res.json(result);
  } catch (error) {
    logger.error('Error validating blueprint:', error);
    res.status(500).json({ error: 'Failed to validate blueprint' });
  }
});

// Blueprint delivery endpoints
app.get('/blueprints/:id/delivery', async (req, res) => {
  try {
    const deliveryData = await blueprintDeliveryService.prepareBlueprintDelivery(req.params.id);
    res.json(deliveryData);
  } catch (error) {
    logger.error('Error preparing blueprint delivery:', error);
    res.status(500).json({ error: 'Failed to prepare blueprint delivery' });
  }
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Blueprint service error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`Blueprint Service running on port ${PORT}`);
  logger.info('Environment:', process.env.NODE_ENV);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
