# Include any files or directories that you don't want to be copied to your
# container here (e.g., local build artifacts, temporary files, etc.).
#
# For more help, visit the .dockerignore file reference guide at
# https://docs.docker.com/engine/reference/builder/#dockerignore-file

# System files
**/.DS_Store
**/__pycache__
**/.venv
**/.classpath
**/.dockerignore
**/.env
**/.env.*
**/.git
**/.gitignore
**/.project
**/.settings
**/.toolstarget
**/.vs
**/.vscode
**/*.*proj.user
**/*.dbmdl
**/*.jfm
**/bin
**/charts
**/docker-compose*
**/compose*
**/Dockerfile*
**/npm-debug.log
**/obj
**/secrets.dev.yaml
**/values.dev.yaml
LICENSE
README.md

# Node.js dependencies and build artifacts
**/node_modules
**/node_modules/**
**/.pnpm
**/.pnpm/**
**/pnpm-lock.yaml
**/package-lock.json
**/yarn.lock
**/.next
**/.next/**
**/dist
**/build
**/coverage
**/coverage/**
# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Version control
.git
.gitignore
.github

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db

# Project specific
data/
logs/
*.log
.env.local
.env.development.local
.env.test.local
.env.production.local

# Large directories and specific problematic paths
mvs-vr-v2/implementation/server/node_modules/
mvs-vr-v2/implementation/server/node_modules/**
mvs-vr-v2/implementation/server/.pnpm/
mvs-vr-v2/implementation/server/.pnpm/**
mvs_project/node_modules/
mvs_project/node_modules/**
mvs_project/.pnpm/
mvs_project/.pnpm/**

# Test and temporary files
**/tests/coverage/
**/tests/results/
**/temp/
**/tmp/
**/.cache/
**/logs/
**/data/
**/*.log

# Build and deployment artifacts
**/server.zip
**/.terraform/
**/terraform.tfstate*