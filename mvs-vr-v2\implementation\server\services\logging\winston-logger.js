/**
 * Enhanced Winston Logger Service
 * Provides structured logging with multiple transports, log rotation, and monitoring integration
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');

class EnhancedLogger {
  constructor(options = {}) {
    this.options = {
      level: process.env.LOG_LEVEL || 'info',
      service: options.service || 'mvs-vr-server',
      environment: process.env.NODE_ENV || 'development',
      logDir: options.logDir || path.join(process.cwd(), 'logs'),
      enableConsole: options.enableConsole !== false,
      enableFile: options.enableFile !== false,
      enableRotation: options.enableRotation !== false,
      maxFiles: options.maxFiles || '14d',
      maxSize: options.maxSize || '20m',
      ...options
    };

    this.createLogDirectory();
    this.logger = this.createLogger();
    this.setupErrorHandling();
  }

  createLogDirectory() {
    if (!fs.existsSync(this.options.logDir)) {
      fs.mkdirSync(this.options.logDir, { recursive: true });
    }
  }

  createLogger() {
    const transports = [];

    // Console transport
    if (this.options.enableConsole) {
      transports.push(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp(),
          winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
            return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
          })
        )
      }));
    }

    // File transports
    if (this.options.enableFile) {
      // Error log
      transports.push(new DailyRotateFile({
        filename: path.join(this.options.logDir, 'error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        maxFiles: this.options.maxFiles,
        maxSize: this.options.maxSize,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        )
      }));

      // Combined log
      transports.push(new DailyRotateFile({
        filename: path.join(this.options.logDir, 'combined-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        maxFiles: this.options.maxFiles,
        maxSize: this.options.maxSize,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        )
      }));

      // Access log for HTTP requests
      transports.push(new DailyRotateFile({
        filename: path.join(this.options.logDir, 'access-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'http',
        maxFiles: this.options.maxFiles,
        maxSize: this.options.maxSize,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        )
      }));
    }

    return winston.createLogger({
      level: this.options.level,
      defaultMeta: {
        service: this.options.service,
        environment: this.options.environment,
        hostname: require('os').hostname(),
        pid: process.pid
      },
      transports,
      exceptionHandlers: this.options.enableFile ? [
        new DailyRotateFile({
          filename: path.join(this.options.logDir, 'exceptions-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxFiles: this.options.maxFiles,
          maxSize: this.options.maxSize
        })
      ] : [],
      rejectionHandlers: this.options.enableFile ? [
        new DailyRotateFile({
          filename: path.join(this.options.logDir, 'rejections-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxFiles: this.options.maxFiles,
          maxSize: this.options.maxSize
        })
      ] : []
    });
  }

  setupErrorHandling() {
    // Handle logger errors
    this.logger.on('error', (error) => {
      console.error('Logger error:', error);
    });

    // Handle file rotation events
    this.logger.transports.forEach(transport => {
      if (transport instanceof DailyRotateFile) {
        transport.on('rotate', (oldFilename, newFilename) => {
          console.log(`Log rotated: ${oldFilename} -> ${newFilename}`);
        });

        transport.on('archive', (zipFilename) => {
          console.log(`Log archived: ${zipFilename}`);
        });
      }
    });
  }

  // Standard logging methods
  error(message, meta = {}) {
    this.logger.error(message, { ...meta, timestamp: new Date().toISOString() });
  }

  warn(message, meta = {}) {
    this.logger.warn(message, { ...meta, timestamp: new Date().toISOString() });
  }

  info(message, meta = {}) {
    this.logger.info(message, { ...meta, timestamp: new Date().toISOString() });
  }

  debug(message, meta = {}) {
    this.logger.debug(message, { ...meta, timestamp: new Date().toISOString() });
  }

  verbose(message, meta = {}) {
    this.logger.verbose(message, { ...meta, timestamp: new Date().toISOString() });
  }

  // HTTP request logging
  http(message, meta = {}) {
    this.logger.http(message, { ...meta, timestamp: new Date().toISOString() });
  }

  // Structured logging methods
  logRequest(req, res, responseTime) {
    this.http('HTTP Request', {
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: res.get('Content-Length')
    });
  }

  logError(error, context = {}) {
    this.error('Application Error', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context
    });
  }

  logPerformance(operation, duration, metadata = {}) {
    this.info('Performance Metric', {
      operation,
      duration: `${duration}ms`,
      ...metadata
    });
  }

  logSecurity(event, details = {}) {
    this.warn('Security Event', {
      event,
      ...details,
      severity: 'security'
    });
  }

  logDatabase(query, duration, rows = 0) {
    this.debug('Database Query', {
      query: query.substring(0, 200), // Truncate long queries
      duration: `${duration}ms`,
      rows,
      type: 'database'
    });
  }

  logAuth(event, userId, details = {}) {
    this.info('Authentication Event', {
      event,
      userId,
      ...details,
      type: 'authentication'
    });
  }

  // Business logic logging
  logBusinessEvent(event, data = {}) {
    this.info('Business Event', {
      event,
      ...data,
      type: 'business'
    });
  }

  // System monitoring
  logSystemMetrics(metrics) {
    this.info('System Metrics', {
      ...metrics,
      type: 'metrics'
    });
  }

  // Get logger instance for direct use
  getLogger() {
    return this.logger;
  }

  // Create child logger with additional context
  child(context) {
    return {
      error: (message, meta = {}) => this.error(message, { ...context, ...meta }),
      warn: (message, meta = {}) => this.warn(message, { ...context, ...meta }),
      info: (message, meta = {}) => this.info(message, { ...context, ...meta }),
      debug: (message, meta = {}) => this.debug(message, { ...context, ...meta }),
      verbose: (message, meta = {}) => this.verbose(message, { ...context, ...meta }),
      http: (message, meta = {}) => this.http(message, { ...context, ...meta })
    };
  }

  // Cleanup method
  close() {
    return new Promise((resolve) => {
      this.logger.close(() => {
        resolve();
      });
    });
  }
}

// Singleton instance
let instance = null;

function createLogger(options = {}) {
  if (!instance) {
    instance = new EnhancedLogger(options);
  }
  return instance;
}

function getLogger() {
  if (!instance) {
    instance = new EnhancedLogger();
  }
  return instance;
}

module.exports = {
  EnhancedLogger,
  createLogger,
  getLogger
};
