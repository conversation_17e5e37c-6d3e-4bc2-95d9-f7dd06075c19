/**
 * Scene Management Service Server
 * Handles scene configuration, validation, and delivery
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { logger } from '../../shared/utils/logger';
import { SceneService } from './scene-service';
import { SceneValidator } from './scene-validator';
import { SceneDeliveryService } from './scene-delivery-service';

const app = express();
const PORT = process.env.SERVICE_PORT || 3002;

// Initialize services
const sceneService = new SceneService();
const sceneValidator = new SceneValidator();
const sceneDeliveryService = new SceneDeliveryService();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'scene-service',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Scene endpoints
app.get('/scenes', async (req, res) => {
  try {
    const scenes = await sceneService.listScenes(req.query);
    res.json(scenes);
  } catch (error) {
    logger.error('Error listing scenes:', error);
    res.status(500).json({ error: 'Failed to list scenes' });
  }
});

app.get('/scenes/:id', async (req, res) => {
  try {
    const scene = await sceneService.getScene(req.params.id);
    if (!scene) {
      return res.status(404).json({ error: 'Scene not found' });
    }
    res.json(scene);
  } catch (error) {
    logger.error('Error getting scene:', error);
    res.status(500).json({ error: 'Failed to get scene' });
  }
});

app.post('/scenes', async (req, res) => {
  try {
    const scene = await sceneService.createScene(req.body);
    res.status(201).json(scene);
  } catch (error) {
    logger.error('Error creating scene:', error);
    res.status(500).json({ error: 'Failed to create scene' });
  }
});

app.put('/scenes/:id', async (req, res) => {
  try {
    const scene = await sceneService.updateScene(req.params.id, req.body);
    res.json(scene);
  } catch (error) {
    logger.error('Error updating scene:', error);
    res.status(500).json({ error: 'Failed to update scene' });
  }
});

app.delete('/scenes/:id', async (req, res) => {
  try {
    await sceneService.deleteScene(req.params.id);
    res.status(204).send();
  } catch (error) {
    logger.error('Error deleting scene:', error);
    res.status(500).json({ error: 'Failed to delete scene' });
  }
});

// Scene validation endpoints
app.post('/scenes/:id/validate', async (req, res) => {
  try {
    const result = await sceneValidator.validateScene(req.params.id);
    res.json(result);
  } catch (error) {
    logger.error('Error validating scene:', error);
    res.status(500).json({ error: 'Failed to validate scene' });
  }
});

// Scene delivery endpoints
app.get('/scenes/:id/delivery', async (req, res) => {
  try {
    const deliveryData = await sceneDeliveryService.prepareSceneDelivery(req.params.id);
    res.json(deliveryData);
  } catch (error) {
    logger.error('Error preparing scene delivery:', error);
    res.status(500).json({ error: 'Failed to prepare scene delivery' });
  }
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Scene service error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`Scene Service running on port ${PORT}`);
  logger.info('Environment:', process.env.NODE_ENV);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
