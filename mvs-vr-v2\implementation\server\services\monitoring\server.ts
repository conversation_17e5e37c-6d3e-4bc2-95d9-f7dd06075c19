/**
 * Monitoring Service Server
 * Handles system monitoring, health checks, and alerting
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { logger } from '../../shared/utils/logger';

const app = express();
const PORT = process.env.SERVICE_PORT || 3007;
const PROMETHEUS_PORT = process.env.PROMETHEUS_PORT || 9090;

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'monitoring-service',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Monitoring endpoints
app.get('/status', async (req, res) => {
  try {
    // TODO: Implement system status check
    res.json({ 
      status: 'operational',
      services: {
        'api-gateway': 'healthy',
        'asset-service': 'healthy',
        'scene-service': 'healthy',
        'blueprint-service': 'healthy',
        'llm-service': 'healthy',
        'auth-service': 'healthy',
        'analytics-service': 'healthy'
      }
    });
  } catch (error) {
    logger.error('Error getting system status:', error);
    res.status(500).json({ error: 'Failed to get system status' });
  }
});

app.get('/metrics', async (req, res) => {
  try {
    // TODO: Implement Prometheus metrics
    res.set('Content-Type', 'text/plain');
    res.send('# Monitoring metrics - implementation needed');
  } catch (error) {
    logger.error('Error getting metrics:', error);
    res.status(500).json({ error: 'Failed to get metrics' });
  }
});

app.post('/alerts', async (req, res) => {
  try {
    // TODO: Implement alert handling
    res.json({ message: 'Alert received and processed' });
  } catch (error) {
    logger.error('Error processing alert:', error);
    res.status(500).json({ error: 'Failed to process alert' });
  }
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Monitoring service error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`Monitoring Service running on port ${PORT}`);
  logger.info('Environment:', process.env.NODE_ENV);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
