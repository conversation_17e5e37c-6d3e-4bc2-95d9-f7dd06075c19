# Start MVS-VR-V2 Microservices
# This script builds and starts all microservices

param (
    [switch]$Build,
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Logs,
    [string]$Service = ""
)

$ErrorActionPreference = "Stop"

function Write-Status {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] ERROR: $Message" -ForegroundColor Red
}

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop."
    exit 1
}

# Set working directory
Set-Location $PSScriptRoot\..

# Check if .env file exists
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Write-Status "Creating .env file from .env.example..."
        Copy-Item ".env.example" ".env"
        Write-Status "Please edit .env file with your configuration before continuing."
        exit 0
    } else {
        Write-Error ".env file not found. Please create one based on .env.example"
        exit 1
    }
}

# Stop services
if ($Stop -or $Restart) {
    Write-Status "Stopping microservices..."
    docker-compose down
    if ($Stop) {
        Write-Success "All services stopped."
        exit 0
    }
}

# Build services
if ($Build -or $Restart) {
    Write-Status "Building microservices..."
    docker-compose build --parallel
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed."
        exit 1
    }
    Write-Success "Build completed successfully."
}

# Show logs for specific service
if ($Logs -and $Service) {
    Write-Status "Showing logs for $Service..."
    docker-compose logs -f $Service
    exit 0
}

# Show logs for all services
if ($Logs) {
    Write-Status "Showing logs for all services..."
    docker-compose logs -f
    exit 0
}

# Start services
Write-Status "Starting microservices..."
docker-compose up -d

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to start services."
    exit 1
}

# Wait a moment for services to start
Start-Sleep -Seconds 5

# Check service health
Write-Status "Checking service health..."
$services = @(
    @{Name="API Gateway"; Port=3000; Path="/health"},
    @{Name="Asset Service"; Port=3001; Path="/health"},
    @{Name="Scene Service"; Port=3002; Path="/health"},
    @{Name="Blueprint Service"; Port=3003; Path="/health"},
    @{Name="LLM Service"; Port=3004; Path="/health"},
    @{Name="Auth Service"; Port=3005; Path="/health"},
    @{Name="Analytics Service"; Port=3006; Path="/health"},
    @{Name="Monitoring Service"; Port=3007; Path="/health"},
    @{Name="Directus"; Port=8055; Path="/server/health"},
    @{Name="Redis"; Port=6379; Path=""}
)

$healthyServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    try {
        if ($service.Port -eq 6379) {
            # Special check for Redis
            $result = docker-compose exec -T redis redis-cli ping 2>$null
            if ($result -eq "PONG") {
                Write-Success "$($service.Name) is healthy"
                $healthyServices++
            } else {
                Write-Error "$($service.Name) is not responding"
            }
        } else {
            $response = Invoke-WebRequest -Uri "http://localhost:$($service.Port)$($service.Path)" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "$($service.Name) is healthy"
                $healthyServices++
            } else {
                Write-Error "$($service.Name) returned status $($response.StatusCode)"
            }
        }
    } catch {
        Write-Error "$($service.Name) is not responding: $($_.Exception.Message)"
    }
}

Write-Status "Health check complete: $healthyServices/$totalServices services are healthy"

if ($healthyServices -eq $totalServices) {
    Write-Success "All microservices are running successfully!"
    Write-Host ""
    Write-Host "Service URLs:" -ForegroundColor Yellow
    Write-Host "  API Gateway:      http://localhost:3000" -ForegroundColor Gray
    Write-Host "  Asset Service:    http://localhost:3001" -ForegroundColor Gray
    Write-Host "  Scene Service:    http://localhost:3002" -ForegroundColor Gray
    Write-Host "  Blueprint Service: http://localhost:3003" -ForegroundColor Gray
    Write-Host "  LLM Service:      http://localhost:3004" -ForegroundColor Gray
    Write-Host "  Auth Service:     http://localhost:3005" -ForegroundColor Gray
    Write-Host "  Analytics Service: http://localhost:3006" -ForegroundColor Gray
    Write-Host "  Monitoring Service: http://localhost:3007" -ForegroundColor Gray
    Write-Host "  Directus Admin:   http://localhost:8055" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Use 'docker-compose logs -f [service-name]' to view logs" -ForegroundColor Yellow
    Write-Host "Use 'docker-compose ps' to view service status" -ForegroundColor Yellow
} else {
    Write-Error "Some services are not healthy. Check the logs for more information."
    Write-Host "Use: docker-compose logs [service-name]" -ForegroundColor Yellow
    exit 1
}
