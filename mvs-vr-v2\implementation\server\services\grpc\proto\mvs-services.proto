syntax = "proto3";

package mvs.services;

// Common message types
message Empty {}

message Status {
  bool success = 1;
  string message = 2;
  int32 code = 3;
}

message HealthCheckRequest {
  string service = 1;
}

message HealthCheckResponse {
  enum ServingStatus {
    UNKNOWN = 0;
    SERVING = 1;
    NOT_SERVING = 2;
    SERVICE_UNKNOWN = 3;
  }
  ServingStatus status = 1;
}

// Asset Service Messages
message Asset {
  string id = 1;
  string name = 2;
  string type = 3;
  string url = 4;
  int64 size = 5;
  string checksum = 6;
  string created_at = 7;
  string updated_at = 8;
  map<string, string> metadata = 9;
}

message GetAssetRequest {
  string id = 1;
}

message GetAssetResponse {
  Asset asset = 1;
  Status status = 2;
}

message CreateAssetRequest {
  Asset asset = 1;
}

message CreateAssetResponse {
  Asset asset = 1;
  Status status = 2;
}

message UpdateAssetRequest {
  Asset asset = 1;
}

message UpdateAssetResponse {
  Asset asset = 1;
  Status status = 2;
}

message DeleteAssetRequest {
  string id = 1;
}

message DeleteAssetResponse {
  Status status = 1;
}

message ListAssetsRequest {
  int32 page = 1;
  int32 limit = 2;
  string filter = 3;
}

message ListAssetsResponse {
  repeated Asset assets = 1;
  int32 total = 2;
  Status status = 3;
}

// User Service Messages
message User {
  string id = 1;
  string email = 2;
  string name = 3;
  string role = 4;
  bool active = 5;
  string created_at = 6;
  string updated_at = 7;
  map<string, string> metadata = 8;
}

message GetUserRequest {
  string id = 1;
}

message GetUserResponse {
  User user = 1;
  Status status = 2;
}

message CreateUserRequest {
  User user = 1;
}

message CreateUserResponse {
  User user = 1;
  Status status = 2;
}

message UpdateUserRequest {
  User user = 1;
}

message UpdateUserResponse {
  User user = 1;
  Status status = 2;
}

message DeleteUserRequest {
  string id = 1;
}

message DeleteUserResponse {
  Status status = 1;
}

// Analytics Service Messages
message AnalyticsEvent {
  string id = 1;
  string event_type = 2;
  string user_id = 3;
  string session_id = 4;
  map<string, string> properties = 5;
  string timestamp = 6;
}

message TrackEventRequest {
  AnalyticsEvent event = 1;
}

message TrackEventResponse {
  Status status = 1;
}

message GetAnalyticsRequest {
  string user_id = 1;
  string start_date = 2;
  string end_date = 3;
  repeated string event_types = 4;
}

message GetAnalyticsResponse {
  repeated AnalyticsEvent events = 1;
  map<string, int32> aggregations = 2;
  Status status = 3;
}

// Scene Service Messages
message Scene {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string asset_ids = 4;
  map<string, string> configuration = 5;
  string created_at = 6;
  string updated_at = 7;
}

message GetSceneRequest {
  string id = 1;
}

message GetSceneResponse {
  Scene scene = 1;
  Status status = 2;
}

message CreateSceneRequest {
  Scene scene = 1;
}

message CreateSceneResponse {
  Scene scene = 1;
  Status status = 2;
}

message UpdateSceneRequest {
  Scene scene = 1;
}

message UpdateSceneResponse {
  Scene scene = 1;
  Status status = 2;
}

message DeleteSceneRequest {
  string id = 1;
}

message DeleteSceneResponse {
  Status status = 1;
}

// Service Definitions
service HealthService {
  rpc Check(HealthCheckRequest) returns (HealthCheckResponse);
}

service AssetService {
  rpc GetAsset(GetAssetRequest) returns (GetAssetResponse);
  rpc CreateAsset(CreateAssetRequest) returns (CreateAssetResponse);
  rpc UpdateAsset(UpdateAssetRequest) returns (UpdateAssetResponse);
  rpc DeleteAsset(DeleteAssetRequest) returns (DeleteAssetResponse);
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse);
}

service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse);
}

service AnalyticsService {
  rpc TrackEvent(TrackEventRequest) returns (TrackEventResponse);
  rpc GetAnalytics(GetAnalyticsRequest) returns (GetAnalyticsResponse);
}

service SceneService {
  rpc GetScene(GetSceneRequest) returns (GetSceneResponse);
  rpc CreateScene(CreateSceneRequest) returns (CreateSceneResponse);
  rpc UpdateScene(UpdateSceneRequest) returns (UpdateSceneResponse);
  rpc DeleteScene(DeleteSceneRequest) returns (DeleteSceneResponse);
}
