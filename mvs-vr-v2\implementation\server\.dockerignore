# Include any files or directories that you don't want to be copied to your
# container here (e.g., local build artifacts, temporary files, etc.).
#
# For more help, visit the .dockerignore file reference guide at
# https://docs.docker.com/engine/reference/builder/#dockerignore-file

**/.DS_Store
**/__pycache__
**/.venv
**/.classpath
**/.dockerignore
**/.env
**/.git
**/.gitignore
**/.project
**/.settings
**/.toolstarget
**/.vs
**/.vscode
**/*.*proj.user
**/*.dbmdl
**/*.jfm
**/bin
**/charts
**/docker-compose*
**/compose*
**/Dockerfile*
**/node_modules
**/npm-debug.log
**/obj
**/secrets.dev.yaml
**/values.dev.yaml
LICENSE
README.md
# Dependencies
node_modules
npm-debug.log
.pnpm
# pnpm-lock.yaml - needed for Docker build
package-lock.json
yarn.lock

# Build outputs
dist
build
.next
coverage
.nyc_output

# Development files
.git
.gitignore
README.md
*.md
.env*
.DS_Store
.vscode
.idea

# Test files
tests
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js
test-*
**/test-*

# Logs and temp files
logs
*.log
temp
tmp
.cache

# Docker files
Dockerfile*
docker-compose*
.dockerignore
