/**
 * gRPC Service Implementation
 * Provides inter-service communication using gRPC protocol
 */

const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const { getLogger } = require('../logging/winston-logger');

const logger = getLogger();

class GRPCService {
  constructor(options = {}) {
    this.options = {
      host: options.host || '0.0.0.0',
      port: options.port || 50051,
      protoPath: options.protoPath || path.join(__dirname, 'proto/mvs-services.proto'),
      ...options
    };
    
    this.server = null;
    this.clients = new Map();
    this.services = new Map();
    this.packageDefinition = null;
    this.proto = null;
  }

  async initialize() {
    try {
      // Load proto file
      this.packageDefinition = protoLoader.loadSync(this.options.protoPath, {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true
      });

      this.proto = grpc.loadPackageDefinition(this.packageDefinition);
      
      // Create gRPC server
      this.server = new grpc.Server();
      
      logger.info('gRPC service initialized successfully');
    } catch (error) {
      logger.logError(error, { context: 'GRPCService.initialize' });
      throw error;
    }
  }

  // Register a service implementation
  registerService(serviceName, implementation) {
    try {
      const serviceDefinition = this.proto.mvs.services[serviceName];
      
      if (!serviceDefinition) {
        throw new Error(`Service ${serviceName} not found in proto definition`);
      }

      this.server.addService(serviceDefinition.service, implementation);
      this.services.set(serviceName, implementation);
      
      logger.info(`gRPC service ${serviceName} registered`);
    } catch (error) {
      logger.logError(error, { context: `GRPCService.registerService - ${serviceName}` });
      throw error;
    }
  }

  // Start the gRPC server
  async start() {
    return new Promise((resolve, reject) => {
      const bindAddress = `${this.options.host}:${this.options.port}`;
      
      this.server.bindAsync(
        bindAddress,
        grpc.ServerCredentials.createInsecure(),
        (error, port) => {
          if (error) {
            logger.logError(error, { context: 'GRPCService.start' });
            reject(error);
            return;
          }

          this.server.start();
          logger.info(`gRPC server started on ${bindAddress}`);
          resolve(port);
        }
      );
    });
  }

  // Stop the gRPC server
  async stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.tryShutdown((error) => {
          if (error) {
            logger.logError(error, { context: 'GRPCService.stop' });
          } else {
            logger.info('gRPC server stopped');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  // Create a client for a specific service
  createClient(serviceName, address) {
    try {
      const serviceDefinition = this.proto.mvs.services[serviceName];
      
      if (!serviceDefinition) {
        throw new Error(`Service ${serviceName} not found in proto definition`);
      }

      const client = new serviceDefinition(
        address,
        grpc.credentials.createInsecure()
      );

      this.clients.set(`${serviceName}:${address}`, client);
      
      logger.info(`gRPC client created for ${serviceName} at ${address}`);
      return client;
    } catch (error) {
      logger.logError(error, { context: `GRPCService.createClient - ${serviceName}` });
      throw error;
    }
  }

  // Get an existing client
  getClient(serviceName, address) {
    const key = `${serviceName}:${address}`;
    return this.clients.get(key);
  }

  // Call a remote service method
  async callService(serviceName, address, method, request, options = {}) {
    try {
      let client = this.getClient(serviceName, address);
      
      if (!client) {
        client = this.createClient(serviceName, address);
      }

      return new Promise((resolve, reject) => {
        const deadline = new Date();
        deadline.setSeconds(deadline.getSeconds() + (options.timeout || 30));

        client[method](request, { deadline }, (error, response) => {
          if (error) {
            logger.logError(error, { 
              context: `GRPCService.callService - ${serviceName}.${method}`,
              request,
              address
            });
            reject(error);
          } else {
            logger.debug(`gRPC call successful: ${serviceName}.${method}`, {
              address,
              responseStatus: response.status
            });
            resolve(response);
          }
        });
      });
    } catch (error) {
      logger.logError(error, { context: `GRPCService.callService - ${serviceName}.${method}` });
      throw error;
    }
  }

  // Health check implementation
  getHealthService() {
    return {
      check: (call, callback) => {
        const serviceName = call.request.service;
        
        // Check if service is registered and healthy
        const isHealthy = this.services.has(serviceName) || serviceName === '';
        
        const response = {
          status: isHealthy ? 1 : 2 // SERVING : NOT_SERVING
        };

        logger.debug(`Health check for service: ${serviceName}`, { status: response.status });
        callback(null, response);
      }
    };
  }

  // Get service statistics
  getStats() {
    return {
      server: {
        running: !!this.server,
        address: `${this.options.host}:${this.options.port}`,
        registeredServices: Array.from(this.services.keys())
      },
      clients: {
        count: this.clients.size,
        connections: Array.from(this.clients.keys())
      },
      timestamp: new Date().toISOString()
    };
  }

  // Close all clients
  closeAllClients() {
    for (const [key, client] of this.clients.entries()) {
      try {
        client.close();
        logger.debug(`gRPC client closed: ${key}`);
      } catch (error) {
        logger.logError(error, { context: `GRPCService.closeAllClients - ${key}` });
      }
    }
    this.clients.clear();
  }

  // Graceful shutdown
  async shutdown() {
    logger.info('Shutting down gRPC service...');
    
    // Close all clients
    this.closeAllClients();
    
    // Stop server
    await this.stop();
    
    logger.info('gRPC service shutdown complete');
  }
}

// Service implementations
class AssetServiceImpl {
  constructor(assetService) {
    this.assetService = assetService;
  }

  async getAsset(call, callback) {
    try {
      const { id } = call.request;
      const asset = await this.assetService.getAsset(id);
      
      callback(null, {
        asset,
        status: { success: true, message: 'Asset retrieved successfully', code: 200 }
      });
    } catch (error) {
      callback(null, {
        asset: null,
        status: { success: false, message: error.message, code: 500 }
      });
    }
  }

  async createAsset(call, callback) {
    try {
      const { asset } = call.request;
      const createdAsset = await this.assetService.createAsset(asset);
      
      callback(null, {
        asset: createdAsset,
        status: { success: true, message: 'Asset created successfully', code: 201 }
      });
    } catch (error) {
      callback(null, {
        asset: null,
        status: { success: false, message: error.message, code: 500 }
      });
    }
  }

  async updateAsset(call, callback) {
    try {
      const { asset } = call.request;
      const updatedAsset = await this.assetService.updateAsset(asset);
      
      callback(null, {
        asset: updatedAsset,
        status: { success: true, message: 'Asset updated successfully', code: 200 }
      });
    } catch (error) {
      callback(null, {
        asset: null,
        status: { success: false, message: error.message, code: 500 }
      });
    }
  }

  async deleteAsset(call, callback) {
    try {
      const { id } = call.request;
      await this.assetService.deleteAsset(id);
      
      callback(null, {
        status: { success: true, message: 'Asset deleted successfully', code: 200 }
      });
    } catch (error) {
      callback(null, {
        status: { success: false, message: error.message, code: 500 }
      });
    }
  }

  async listAssets(call, callback) {
    try {
      const { page, limit, filter } = call.request;
      const result = await this.assetService.listAssets({ page, limit, filter });
      
      callback(null, {
        assets: result.assets,
        total: result.total,
        status: { success: true, message: 'Assets retrieved successfully', code: 200 }
      });
    } catch (error) {
      callback(null, {
        assets: [],
        total: 0,
        status: { success: false, message: error.message, code: 500 }
      });
    }
  }
}

// Singleton instance
let instance = null;

function getGRPCService(options = {}) {
  if (!instance) {
    instance = new GRPCService(options);
  }
  return instance;
}

module.exports = {
  GRPCService,
  AssetServiceImpl,
  getGRPCService
};
