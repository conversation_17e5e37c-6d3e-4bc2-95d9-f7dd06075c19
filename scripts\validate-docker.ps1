# Docker validation script

Write-Host "Validating Docker setup..." -ForegroundColor Cyan

# Check if Dock<PERSON> is running
try {
    $dockerInfo = docker info 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Docker is not running. Please start Docker Desktop." -ForegroundColor Red
        exit 1
    }
    Write-Host "Docker is running correctly." -ForegroundColor Green
} catch {
    Write-Host "ERROR: Docker command failed. Is Docker installed?" -ForegroundColor Red
    exit 1
}

# Check if requirements.txt exists
if (-not (Test-Path "requirements.txt")) {
    Write-Host "ERROR: requirements.txt not found." -ForegroundColor Red
    exit 1
}

# Check Python version in Dockerfile
$dockerfile = Get-Content "Dockerfile" -ErrorAction SilentlyContinue
if ($null -eq $dockerfile) {
    Write-Host "ERROR: Dockerfile not found." -ForegroundColor Red
    exit 1
}

$pythonVersion = $dockerfile | Select-String -Pattern "FROM python:(.*)-slim" | ForEach-Object { $_.Matches.Groups[1].Value }
if ([version]$pythonVersion -lt [version]"3.10") {
    Write-Host "WARNING: Python version in Dockerfile is $pythonVersion, but some dependencies require 3.10+." -ForegroundColor Yellow
    Write-Host "Consider updating to Python 3.11 in your Dockerfile." -ForegroundColor Yellow
}

# Check docker-compose.yml
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "ERROR: docker-compose.yml not found." -ForegroundColor Red
    exit 1
}

# Check docker-compose syntax
try {
    $composeCheck = docker-compose config 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: docker-compose.yml has syntax errors:" -ForegroundColor Red
        Write-Host $composeCheck -ForegroundColor Red
        exit 1
    }
    Write-Host "docker-compose.yml syntax is valid." -ForegroundColor Green
} catch {
    Write-Host "ERROR: docker-compose command failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Docker validation completed successfully!" -ForegroundColor Cyan
exit 0