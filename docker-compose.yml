services:
  llm-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '8000:8000'
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8000/health']
      interval: 10s
      timeout: 5s
      retries: 3

  load-test:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
    depends_on:
      llm-service:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1
      - TARGET_HOST=http://llm-service:8000
    command: >
      bash -c "
        echo 'Running manual tests...' &&
        python tests/load_test_llm.py &&
        echo 'Starting load test...' &&
        locust -f tests/load_test_llm.py --host http://llm-service:8000 --headless -u 1 -r 1 --run-time 30s
      "

networks:
  default:
    driver: bridge
