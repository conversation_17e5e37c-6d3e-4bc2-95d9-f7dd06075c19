# Cleanup Node Modules <PERSON>ript
# This script removes problematic node_modules directories that can cause Docker build issues

param (
    [switch]$DryRun,
    [switch]$Force
)

$ErrorActionPreference = "Stop"

function Write-Status {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] $Message" -ForegroundColor Cyan
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] WARNING: $Message" -ForegroundColor Yellow
}

function Write-Success {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] $Message" -ForegroundColor Green
}

# Define paths to clean
$pathsToClean = @(
    "mvs-vr-v2/implementation/server/node_modules",
    "mvs-vr-v2/implementation/server/.pnpm",
    "mvs_project/node_modules",
    "mvs_project/.pnpm",
    "mvs-vr/node_modules",
    "mvs-vr/.pnpm"
)

Write-Status "Node.js cleanup script starting..."

if ($DryRun) {
    Write-Status "DRY RUN MODE - No files will be deleted"
}

$totalSize = 0
$pathsFound = @()

# Check which paths exist and calculate total size
foreach ($path in $pathsToClean) {
    if (Test-Path $path) {
        try {
            $size = (Get-ChildItem -Path $path -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
            $sizeGB = [math]::Round($size / 1GB, 2)
            $pathsFound += @{
                Path = $path
                Size = $size
                SizeGB = $sizeGB
            }
            $totalSize += $size
            Write-Status "Found: $path ($sizeGB GB)"
        } catch {
            Write-Warning "Could not calculate size for $path"
            $pathsFound += @{
                Path = $path
                Size = 0
                SizeGB = 0
            }
        }
    }
}

if ($pathsFound.Count -eq 0) {
    Write-Success "No node_modules directories found to clean."
    exit 0
}

$totalSizeGB = [math]::Round($totalSize / 1GB, 2)
Write-Status "Total size to be freed: $totalSizeGB GB"

if (-not $DryRun) {
    if (-not $Force) {
        $response = Read-Host "Do you want to proceed with deletion? (y/N)"
        if ($response -ne 'y' -and $response -ne 'Y') {
            Write-Status "Operation cancelled by user."
            exit 0
        }
    }

    Write-Status "Starting cleanup..."
    
    foreach ($pathInfo in $pathsFound) {
        $path = $pathInfo.Path
        Write-Status "Removing $path..."
        
        try {
            Remove-Item -Path $path -Recurse -Force -ErrorAction Stop
            Write-Success "Successfully removed $path"
        } catch {
            Write-Warning "Failed to remove $path`: $_"
        }
    }
    
    Write-Success "Cleanup completed! Freed approximately $totalSizeGB GB of disk space."
    Write-Status "You can now run the Docker build script."
} else {
    Write-Status "DRY RUN completed. Use -Force to skip confirmation or run without -DryRun to proceed."
}
