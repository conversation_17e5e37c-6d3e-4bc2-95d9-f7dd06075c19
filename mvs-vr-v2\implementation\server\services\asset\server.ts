/**
 * Asset Management Service Server
 * Handles asset upload, processing, storage, and delivery
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import multer from 'multer';
import { logger } from '../../shared/utils/logger';
import { AssetService } from './asset-service';
import { AssetProcessor } from './asset-processor';
import { AssetBundleService } from './asset-bundle-service';

const app = express();
const PORT = process.env.SERVICE_PORT || 3001;

// Initialize services
const assetService = new AssetService();
const assetProcessor = new AssetProcessor();
const assetBundleService = new AssetBundleService();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true }));

// Multer configuration for file uploads
const upload = multer({
  dest: '/app/uploads/',
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'asset-service',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Asset endpoints
app.get('/assets', async (req, res) => {
  try {
    const assets = await assetService.listAssets(req.query);
    res.json(assets);
  } catch (error) {
    logger.error('Error listing assets:', error);
    res.status(500).json({ error: 'Failed to list assets' });
  }
});

app.get('/assets/:id', async (req, res) => {
  try {
    const asset = await assetService.getAsset(req.params.id);
    if (!asset) {
      return res.status(404).json({ error: 'Asset not found' });
    }
    res.json(asset);
  } catch (error) {
    logger.error('Error getting asset:', error);
    res.status(500).json({ error: 'Failed to get asset' });
  }
});

app.post('/assets', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    const asset = await assetService.uploadAsset(req.file, req.body);
    res.status(201).json(asset);
  } catch (error) {
    logger.error('Error uploading asset:', error);
    res.status(500).json({ error: 'Failed to upload asset' });
  }
});

app.put('/assets/:id', async (req, res) => {
  try {
    const asset = await assetService.updateAsset(req.params.id, req.body);
    res.json(asset);
  } catch (error) {
    logger.error('Error updating asset:', error);
    res.status(500).json({ error: 'Failed to update asset' });
  }
});

app.delete('/assets/:id', async (req, res) => {
  try {
    await assetService.deleteAsset(req.params.id);
    res.status(204).send();
  } catch (error) {
    logger.error('Error deleting asset:', error);
    res.status(500).json({ error: 'Failed to delete asset' });
  }
});

// Asset bundle endpoints
app.get('/bundles', async (req, res) => {
  try {
    const bundles = await assetBundleService.listBundles(req.query);
    res.json(bundles);
  } catch (error) {
    logger.error('Error listing bundles:', error);
    res.status(500).json({ error: 'Failed to list bundles' });
  }
});

app.post('/bundles', async (req, res) => {
  try {
    const bundle = await assetBundleService.createBundle(req.body);
    res.status(201).json(bundle);
  } catch (error) {
    logger.error('Error creating bundle:', error);
    res.status(500).json({ error: 'Failed to create bundle' });
  }
});

// Asset processing endpoints
app.post('/assets/:id/process', async (req, res) => {
  try {
    const result = await assetProcessor.processAsset(req.params.id, req.body);
    res.json(result);
  } catch (error) {
    logger.error('Error processing asset:', error);
    res.status(500).json({ error: 'Failed to process asset' });
  }
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Asset service error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`Asset Service running on port ${PORT}`);
  logger.info('Environment:', process.env.NODE_ENV);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
