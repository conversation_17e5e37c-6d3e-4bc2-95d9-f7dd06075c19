/**
 * Service-Specific Database Configuration
 * Manages different database connections for different services
 */

const { Pool } = require('pg');
const Redis = require('ioredis');
const { getLogger } = require('../logging/winston-logger');

const logger = getLogger();

class ServiceDatabases {
  constructor() {
    this.connections = new Map();
    this.healthChecks = new Map();
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      // Initialize PostgreSQL connections for different services
      await this.initializePostgreSQL();
      
      // Initialize Redis connections
      await this.initializeRedis();
      
      // Initialize other databases if needed
      await this.initializeSpecializedDatabases();
      
      this.initialized = true;
      logger.info('Service databases initialized successfully');
    } catch (error) {
      logger.logError(error, { context: 'ServiceDatabases.initialize' });
      throw error;
    }
  }

  async initializePostgreSQL() {
    const databases = {
      // User service database
      users: {
        host: process.env.USER_DB_HOST || process.env.DB_HOST || 'localhost',
        port: process.env.USER_DB_PORT || process.env.DB_PORT || 5432,
        database: process.env.USER_DB_NAME || 'mvs_users',
        user: process.env.USER_DB_USER || process.env.DB_USER || 'postgres',
        password: process.env.USER_DB_PASSWORD || process.env.DB_PASSWORD || 'password',
        ssl: process.env.USER_DB_SSL === 'true',
        max: 10,
        min: 2
      },
      
      // Asset service database
      assets: {
        host: process.env.ASSET_DB_HOST || process.env.DB_HOST || 'localhost',
        port: process.env.ASSET_DB_PORT || process.env.DB_PORT || 5432,
        database: process.env.ASSET_DB_NAME || 'mvs_assets',
        user: process.env.ASSET_DB_USER || process.env.DB_USER || 'postgres',
        password: process.env.ASSET_DB_PASSWORD || process.env.DB_PASSWORD || 'password',
        ssl: process.env.ASSET_DB_SSL === 'true',
        max: 15,
        min: 3
      },
      
      // Analytics service database
      analytics: {
        host: process.env.ANALYTICS_DB_HOST || process.env.DB_HOST || 'localhost',
        port: process.env.ANALYTICS_DB_PORT || process.env.DB_PORT || 5432,
        database: process.env.ANALYTICS_DB_NAME || 'mvs_analytics',
        user: process.env.ANALYTICS_DB_USER || process.env.DB_USER || 'postgres',
        password: process.env.ANALYTICS_DB_PASSWORD || process.env.DB_PASSWORD || 'password',
        ssl: process.env.ANALYTICS_DB_SSL === 'true',
        max: 8,
        min: 2
      }
    };

    for (const [serviceName, config] of Object.entries(databases)) {
      try {
        const pool = new Pool({
          ...config,
          connectionTimeoutMillis: 5000,
          statement_timeout: 30000,
          query_timeout: 30000
        });

        // Test connection
        await pool.query('SELECT 1');
        
        this.connections.set(`postgres_${serviceName}`, pool);
        
        // Set up health check
        this.healthChecks.set(`postgres_${serviceName}`, async () => {
          try {
            const result = await pool.query('SELECT 1 as health');
            return result.rows.length > 0;
          } catch (error) {
            logger.logError(error, { context: `PostgreSQL health check - ${serviceName}` });
            return false;
          }
        });

        logger.info(`PostgreSQL connection established for ${serviceName} service`);
      } catch (error) {
        logger.logError(error, { context: `PostgreSQL initialization - ${serviceName}` });
        throw error;
      }
    }
  }

  async initializeRedis() {
    const redisConfigs = {
      // Session storage
      sessions: {
        host: process.env.SESSION_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
        port: process.env.SESSION_REDIS_PORT || process.env.REDIS_PORT || 6379,
        password: process.env.SESSION_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        db: 0,
        keyPrefix: 'session:',
        maxRetriesPerRequest: 3
      },
      
      // Cache storage
      cache: {
        host: process.env.CACHE_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
        port: process.env.CACHE_REDIS_PORT || process.env.REDIS_PORT || 6379,
        password: process.env.CACHE_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        db: 1,
        keyPrefix: 'cache:',
        maxRetriesPerRequest: 3
      },
      
      // Rate limiting
      rateLimit: {
        host: process.env.RATELIMIT_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
        port: process.env.RATELIMIT_REDIS_PORT || process.env.REDIS_PORT || 6379,
        password: process.env.RATELIMIT_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        db: 2,
        keyPrefix: 'rate:',
        maxRetriesPerRequest: 3
      },
      
      // Real-time messaging
      pubsub: {
        host: process.env.PUBSUB_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
        port: process.env.PUBSUB_REDIS_PORT || process.env.REDIS_PORT || 6379,
        password: process.env.PUBSUB_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        db: 3,
        keyPrefix: 'pubsub:',
        maxRetriesPerRequest: 3
      }
    };

    for (const [serviceName, config] of Object.entries(redisConfigs)) {
      try {
        const redis = new Redis(config);
        
        // Test connection
        await redis.ping();
        
        this.connections.set(`redis_${serviceName}`, redis);
        
        // Set up health check
        this.healthChecks.set(`redis_${serviceName}`, async () => {
          try {
            const result = await redis.ping();
            return result === 'PONG';
          } catch (error) {
            logger.logError(error, { context: `Redis health check - ${serviceName}` });
            return false;
          }
        });

        logger.info(`Redis connection established for ${serviceName} service`);
      } catch (error) {
        logger.logError(error, { context: `Redis initialization - ${serviceName}` });
        // Redis failures are not critical for basic functionality
        logger.warn(`Redis service ${serviceName} unavailable, continuing without it`);
      }
    }
  }

  async initializeSpecializedDatabases() {
    // ClickHouse for analytics (if configured)
    if (process.env.CLICKHOUSE_HOST) {
      try {
        // Note: This would require clickhouse client library
        // For now, we'll just log that it's configured
        logger.info('ClickHouse configuration detected for analytics service');
      } catch (error) {
        logger.logError(error, { context: 'ClickHouse initialization' });
      }
    }

    // Elasticsearch for search (if configured)
    if (process.env.ELASTICSEARCH_HOST) {
      try {
        // Note: This would require elasticsearch client library
        // For now, we'll just log that it's configured
        logger.info('Elasticsearch configuration detected for search service');
      } catch (error) {
        logger.logError(error, { context: 'Elasticsearch initialization' });
      }
    }
  }

  // Get database connection for a specific service
  getConnection(serviceName, dbType = 'postgres') {
    const key = `${dbType}_${serviceName}`;
    const connection = this.connections.get(key);
    
    if (!connection) {
      throw new Error(`No ${dbType} connection found for service: ${serviceName}`);
    }
    
    return connection;
  }

  // Get PostgreSQL connection
  getPostgreSQL(serviceName) {
    return this.getConnection(serviceName, 'postgres');
  }

  // Get Redis connection
  getRedis(serviceName) {
    return this.getConnection(serviceName, 'redis');
  }

  // Health check for all connections
  async healthCheck() {
    const results = {};
    
    for (const [serviceName, healthCheckFn] of this.healthChecks.entries()) {
      try {
        const isHealthy = await healthCheckFn();
        results[serviceName] = {
          status: isHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        results[serviceName] = {
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }
    
    return results;
  }

  // Get connection statistics
  getStats() {
    const stats = {
      totalConnections: this.connections.size,
      connectionTypes: {},
      timestamp: new Date().toISOString()
    };

    for (const [key, connection] of this.connections.entries()) {
      const [type, service] = key.split('_');
      
      if (!stats.connectionTypes[type]) {
        stats.connectionTypes[type] = [];
      }
      
      stats.connectionTypes[type].push({
        service,
        status: 'connected'
      });
    }

    return stats;
  }

  // Close all connections
  async close() {
    const closePromises = [];
    
    for (const [key, connection] of this.connections.entries()) {
      if (key.startsWith('postgres_')) {
        closePromises.push(connection.end());
      } else if (key.startsWith('redis_')) {
        closePromises.push(connection.disconnect());
      }
    }
    
    await Promise.all(closePromises);
    this.connections.clear();
    this.healthChecks.clear();
    this.initialized = false;
    
    logger.info('All service database connections closed');
  }
}

// Singleton instance
let instance = null;

function getServiceDatabases() {
  if (!instance) {
    instance = new ServiceDatabases();
  }
  return instance;
}

module.exports = {
  ServiceDatabases,
  getServiceDatabases
};
