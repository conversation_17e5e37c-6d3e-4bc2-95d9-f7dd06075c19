# MVS-VR-V2 Microservices Environment Configuration

# Database Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
DB_DATABASE=postgres
DB_USER=postgres
DB_PASSWORD=postgres

# Redis Configuration
REDIS_PASSWORD=redis_password

# Directus Configuration
DIRECTUS_KEY=directus-secret-key
DIRECTUS_SECRET=directus-secret
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=admin

# JWT Configuration
JWT_SECRET=your-jwt-secret

# AWS S3 Configuration (for Asset Service)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET=mvs-vr-assets

# LLM Service Configuration
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
LOCAL_LLM_URL=http://localhost:11434

# Service URLs (for development)
ASSET_SERVICE_URL=http://asset-service:3001
SCENE_SERVICE_URL=http://scene-service:3002
BLUEPRINT_SERVICE_URL=http://blueprint-service:3003
LLM_SERVICE_URL=http://llm-service:3004
AUTH_SERVICE_URL=http://auth-service:3005
ANALYTICS_SERVICE_URL=http://analytics-service:3006
MONITORING_SERVICE_URL=http://monitoring-service:3007

# Development Configuration
NODE_ENV=production
LOG_LEVEL=info

# Security Configuration
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Performance Configuration
MAX_FILE_SIZE=100MB
CACHE_TTL=3600
WORKER_POOL_SIZE=4
