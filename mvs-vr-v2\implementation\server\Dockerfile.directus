FROM directus/directus:10.8

# Install additional dependencies if needed (commented out due to compatibility issues)
# RUN npm install --no-save \
#   directus-extension-computed-interface \
#   directus-extension-group-tabs-interface

# Copy custom extensions if any
# COPY ./extensions /directus/extensions

# Create necessary directories
RUN mkdir -p /directus/uploads /directus/database /directus/extensions

# Set default environment variables (will be overridden by docker-compose)
ENV DB_CLIENT=pg
ENV DB_HOST=supabase-db
ENV DB_PORT=5432
ENV DB_DATABASE=postgres
ENV DB_USER=postgres

# Expose port
EXPOSE 8055

# Health check
HEALTHCHECK --interval=10s --timeout=5s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8055/server/health || exit 1
