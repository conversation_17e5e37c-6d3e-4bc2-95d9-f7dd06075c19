#!/bin/bash
# MVS-VR-V2 Deployment Script

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
VERSION=$(cat "$PROJECT_ROOT/VERSION" 2>/dev/null || echo "1.0.0")
ENVIRONMENT=${1:-production}
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"
ENV_FILE="$PROJECT_ROOT/.env.$ENVIRONMENT"
REGISTRY="ghcr.io/your-org/mvs-vr"
FORCE_REBUILD=${FORCE_REBUILD:-false}
SKIP_TESTS=${SKIP_TESTS:-false}

# Functions
function write_status() {
  echo -e "\033[1;34m[INFO]\033[0m $1"
}

function write_error() {
  echo -e "\033[1;31m[ERROR]\033[0m $1"
}

function write_success() {
  echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

# Check environment file
if [ ! -f "$ENV_FILE" ]; then
  write_error "Environment file not found: $ENV_FILE"
  exit 1
fi

# Check Docker Compose file
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
  write_error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
  exit 1
fi

# Run tests if not skipped
if [ "$SKIP_TESTS" != "true" ]; then
  write_status "Running tests before deployment"
  cd "$PROJECT_ROOT"
  npm test
  if [ $? -ne 0 ]; then
    write_error "Tests failed. Deployment aborted."
    exit 1
  fi
fi

# Build Docker images
write_status "Building Docker images for $ENVIRONMENT environment"
BUILD_ARGS="--env-file $ENV_FILE -f $DOCKER_COMPOSE_FILE build"
if [ "$FORCE_REBUILD" == "true" ]; then
  BUILD_ARGS="$BUILD_ARGS --no-cache"
fi

docker-compose $BUILD_ARGS
if [ $? -ne 0 ]; then
  write_error "Docker build failed."
  exit 1
fi

# Tag Docker images with version
write_status "Tagging Docker images with version: $VERSION"
IMAGES=("server" "directus")
for IMAGE in "${IMAGES[@]}"; do
  SOURCE_IMAGE="$REGISTRY/$IMAGE"
  TARGET_IMAGE="$REGISTRY/$IMAGE:$VERSION"
  
  docker tag "$SOURCE_IMAGE" "$TARGET_IMAGE"
  if [ $? -ne 0 ]; then
    write_error "Failed to tag image: $SOURCE_IMAGE"
    exit 1
  fi
done

# Push Docker images
write_status "Pushing Docker images to registry"
for IMAGE in "${IMAGES[@]}"; do
  TARGET_IMAGE="$REGISTRY/$IMAGE:$VERSION"
  
  docker push "$TARGET_IMAGE"
  if [ $? -ne 0 ]; then
    write_error "Failed to push image: $TARGET_IMAGE"
    exit 1
  fi
done

# Deploy services
write_status "Deploying services for $ENVIRONMENT environment"
docker-compose --env-file "$ENV_FILE" -f "$DOCKER_COMPOSE_FILE" up -d
if [ $? -ne 0 ]; then
  write_error "Deployment failed."
  exit 1
fi

write_success "Deployment completed successfully!"
write_status "Services are now running with version: $VERSION"
docker-compose -f "$DOCKER_COMPOSE_FILE" ps

exit 0
