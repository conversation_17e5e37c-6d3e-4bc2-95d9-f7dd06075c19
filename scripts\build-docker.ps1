# Build Docker script for MVS-VR-V2 project

param (
    [switch]$SkipValidation,
    [switch]$ForceRebuild,
    [string]$ComposeFile = "mvs-vr-v2/docker-compose.yml",
    [switch]$CleanNodeModules
)

$ErrorActionPreference = "Stop"

function Write-Status {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] $Message" -ForegroundColor Cyan
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] WARNING: $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'HH:mm:ss')] ERROR: $Message" -ForegroundColor Red
}

# Validate Docker setup if not skipped
if (-not $SkipValidation) {
    Write-Status "Validating Docker setup..."
    & ".\scripts\validate-docker.ps1"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker validation failed. Please fix the issues before continuing."
        exit 1
    }
}

# Clean problematic node_modules if requested
if ($CleanNodeModules) {
    Write-Status "Cleaning node_modules directories to prevent build issues..."

    $nodeModulesPaths = @(
        "mvs-vr-v2/implementation/server/node_modules",
        "mvs_project/node_modules",
        "mvs-vr/node_modules"
    )

    foreach ($path in $nodeModulesPaths) {
        if (Test-Path $path) {
            Write-Status "Removing $path..."
            try {
                Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
                Write-Status "Successfully removed $path"
            }
            catch {
                Write-Warning "Could not remove $path - this may cause build issues"
            }
        }
    }
}

# Verify the compose file exists
if (-not (Test-Path $ComposeFile)) {
    Write-Error "Docker compose file not found: $ComposeFile"
    Write-Host "Available compose files:" -ForegroundColor Yellow
    Get-ChildItem -Path . -Name "docker-compose*.yml" -Recurse | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    exit 1
}

Write-Status "Using Docker Compose file: $ComposeFile"

# Stop any running containers
Write-Status "Stopping any running containers..."
docker-compose -f $ComposeFile down
if ($LASTEXITCODE -ne 0) {
    Write-Warning "Failed to stop containers. Continuing anyway..."
}

# Remove dangling images if force rebuild
if ($ForceRebuild) {
    Write-Status "Removing dangling images..."
    docker image prune -f
    Write-Status "Removing unused volumes..."
    docker volume prune -f
}

# Build the containers
Write-Status "Building containers (this may take some time)..."
$buildArgs = @()
if ($ForceRebuild) {
    $buildArgs += "--no-cache"
}

# Add progress output for better visibility
$buildArgs += "--progress=plain"

docker-compose -f $ComposeFile build $buildArgs
if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to build containers."
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Try running with -CleanNodeModules flag" -ForegroundColor Gray
    Write-Host "2. Check .dockerignore file includes node_modules" -ForegroundColor Gray
    Write-Host "3. Ensure sufficient disk space" -ForegroundColor Gray
    Write-Host "4. Try building individual services" -ForegroundColor Gray
    exit 1
}

Write-Status "Docker build completed successfully!" -ForegroundColor Green
Write-Status "You can now run 'docker-compose -f $ComposeFile up -d' to start the containers."

exit 0